using PlayerFAP.Components;
using PlayerFAP.Tags;
using ProjectDawn.Navigation.Sample.Crowd;
using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Physics;
using Unity.Rendering;
using Unity.Transforms;
using UnityEngine;
using UnityEngine.Rendering;
using RagdollSystem;
using Unity.Physics.GraphicsIntegration;

namespace Rukhanka.Test.Pooling
{
    /// <summary>
    /// High-performance pooling system with advanced DOTS optimizations
    /// - Burst-compiled hot paths with managed fallbacks
    /// - Zero-allocation entity lookup via hash maps
    /// - Cached component handles and queries
    /// - Parallel processing support
    /// </summary>
    [UpdateInGroup(typeof(InitializationSystemGroup))]
    [RequireMatchingQueriesForUpdate]
    [BurstCompile]
    public partial struct PoolingSystem : ISystem
    {
        // Cached queries with specific archetypes for maximum performance
        private EntityQuery m_PoolManagerQuery;
        private EntityQuery m_SpawnRequestQuery;
        private EntityQuery m_ReturnRequestQuery;
        private EntityQuery m_PooledEntityQuery;
        private EntityQuery m_PoolCountQuery;
        private EntityQuery m_RagdollEntityQuery;
        private EntityQuery m_EnemyEntityQuery;

        private EntityQueryMask m_AllArchetypesMask;
        
        // Cached component handles (updated once per frame)
        private ComponentTypeHandle<PoolTypeComponent> m_PoolTypeHandle;
        private ComponentTypeHandle<Pooled> m_PooledHandle;
        private ComponentTypeHandle<InUse> m_InUseHandle;
        private ComponentTypeHandle<Disabled> m_DisabledHandle;
        private ComponentTypeHandle<LocalTransform> m_TransformHandle;
        private ComponentTypeHandle<PhysicsVelocity> m_PhysicsVelocityHandle;
        private BufferTypeHandle<PoolEntry> m_PoolEntryHandle;
        private BufferTypeHandle<PoolSpawnRequest> m_SpawnRequestHandle;
        private BufferTypeHandle<PoolReturnRequest> m_ReturnRequestHandle;
        private BufferTypeHandle<RagdollColliderReference> m_RagdollColliderHandle;
        private BufferTypeHandle<Child> m_ChildHandle;
        private BufferTypeHandle<LinkedEntityGroup> m_LinkedEntityHandle;

        // Cached lookups for fast component access
        private ComponentLookup<PoolTypeComponent> m_PoolTypeLookup;
        private ComponentLookup<Pooled> m_PooledLookup;
        private ComponentLookup<InUse> m_InUseLookup;
        private ComponentLookup<Disabled> m_DisabledLookup;
        private ComponentLookup<PhysicsVelocity> m_PhysicsVelocityLookup;
        private ComponentLookup<RagdollCharacterTag> m_RagdollCharacterLookup;
        private ComponentLookup<EnemyTag> m_EnemyTagLookup;
        private ComponentLookup<SpawnerLink> m_SpawnerLinkLookup;
        private BufferLookup<PoolEntry> m_PoolEntryLookup;
        private BufferLookup<RagdollColliderReference> m_RagdollColliderLookup;
        private BufferLookup<Child> m_ChildLookup;
        private BufferLookup<LinkedEntityGroup> m_LinkedEntityLookup;

        // Pre-allocated collections (reused every frame to avoid GC)
        private NativeParallelMultiHashMap<uint, Entity> m_TypeHashToEntityMap;
        private NativeList<Entity> m_TempEntityList;
        private NativeList<PoolTypeComponent> m_TempPoolTypeList;
        private NativeHashMap<uint, int> m_TypeHashCounts;
        private NativeBitArray m_EntityExistenceCache;

        
        
        // Query masks for fast archetype matching
        private EntityQueryMask m_PooledEntityMask;
        private EntityQueryMask m_RagdollEntityMask;
        private EntityQueryMask m_EnemyEntityMask;
        
        // OnCreate cannot be Burst compiled due to managed object creation (EntityQuery)
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<EndInitializationEntityCommandBufferSystem.Singleton>();
            state.RequireForUpdate<PoolManagerTag>();
            
            // Initialize all cached queries with specific archetypes
            InitializeQueries(ref state);
            
            // Initialize all component handles and lookups
            InitializeHandlesAndLookups(ref state);
            
            // Initialize pre-allocated collections
            InitializeCollections();
            
            
            
            // Validate pool prefabs in editor
#if UNITY_EDITOR
            ValidatePoolPrefabs(ref state);
#endif
        }
        
        private void InitializeQueries(ref SystemState state)
        {
            // Pool manager query with exclusion for one-time initialization
            m_PoolManagerQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<PoolManagerTag>(),
                ComponentType.ReadOnly<PoolEntry>(),
                ComponentType.Exclude<PoolInitializedTag>()
            );
            
            // Spawn request query
            m_SpawnRequestQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<PoolManagerTag>(),
                ComponentType.ReadOnly<PoolSpawnRequest>()
            );
            
            // Return request query
            m_ReturnRequestQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<PoolManagerTag>(),
                ComponentType.ReadOnly<PoolReturnRequest>()
            );
            
            // Pooled entities query with disabled filter
            m_PooledEntityQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<Pooled>(),
                ComponentType.ReadOnly<PoolTypeComponent>(),
                ComponentType.ReadOnly<Disabled>()
            );
            
            // Pool count query for initialization
            m_PoolCountQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<Pooled>(),
                ComponentType.ReadOnly<PoolTypeComponent>()
            );
            
            // Ragdoll entities query
            m_RagdollEntityQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<RagdollCharacterTag>(),
                ComponentType.ReadOnly<RagdollColliderReference>()
            );
            
            // Enemy entities query
            m_EnemyEntityQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<EnemyTag>(),
                ComponentType.ReadOnly<HealthComponent>()
            );
            
            // Create query masks for fast archetype matching
            m_PooledEntityMask = m_PooledEntityQuery.GetEntityQueryMask();
            m_RagdollEntityMask = m_RagdollEntityQuery.GetEntityQueryMask();
            m_EnemyEntityMask = m_EnemyEntityQuery.GetEntityQueryMask();
            
            var allQuery = state.EntityManager.CreateEntityQuery(ComponentType.ReadOnly<LocalTransform>());
            m_AllArchetypesMask = allQuery.GetEntityQueryMask();
            allQuery.Dispose();  
        }
        
        private void InitializeHandlesAndLookups(ref SystemState state)
        {
            // Component type handles (will be updated each frame)
            m_PoolTypeHandle = state.GetComponentTypeHandle<PoolTypeComponent>(true);
            m_PooledHandle = state.GetComponentTypeHandle<Pooled>(false);
            m_InUseHandle = state.GetComponentTypeHandle<InUse>(false);
            m_DisabledHandle = state.GetComponentTypeHandle<Disabled>(false);
            m_TransformHandle = state.GetComponentTypeHandle<LocalTransform>(false);
            m_PhysicsVelocityHandle = state.GetComponentTypeHandle<PhysicsVelocity>(false);
            
            // Buffer type handles
            m_PoolEntryHandle = state.GetBufferTypeHandle<PoolEntry>(true);
            m_SpawnRequestHandle = state.GetBufferTypeHandle<PoolSpawnRequest>(false);
            m_ReturnRequestHandle = state.GetBufferTypeHandle<PoolReturnRequest>(false);
            m_RagdollColliderHandle = state.GetBufferTypeHandle<RagdollColliderReference>(true);
            m_ChildHandle = state.GetBufferTypeHandle<Child>(true);
            m_LinkedEntityHandle = state.GetBufferTypeHandle<LinkedEntityGroup>(true);
            
            // Component lookups for random access
            m_PoolTypeLookup = state.GetComponentLookup<PoolTypeComponent>(true);
            m_PooledLookup = state.GetComponentLookup<Pooled>(false);
            m_InUseLookup = state.GetComponentLookup<InUse>(false);
            m_DisabledLookup = state.GetComponentLookup<Disabled>(false);
            m_PhysicsVelocityLookup = state.GetComponentLookup<PhysicsVelocity>(false);
            m_RagdollCharacterLookup = state.GetComponentLookup<RagdollCharacterTag>(true);
            m_EnemyTagLookup = state.GetComponentLookup<EnemyTag>(true);
            m_SpawnerLinkLookup = state.GetComponentLookup<SpawnerLink>(false);
            
            // Buffer lookups
            m_PoolEntryLookup = state.GetBufferLookup<PoolEntry>(true);
            m_RagdollColliderLookup = state.GetBufferLookup<RagdollColliderReference>(true);
            m_ChildLookup = state.GetBufferLookup<Child>(true);
            m_LinkedEntityLookup = state.GetBufferLookup<LinkedEntityGroup>(true);
        }
        
        private void InitializeCollections()
        {
            // Pre-allocate collections with reasonable initial capacity
            m_TypeHashToEntityMap = new NativeParallelMultiHashMap<uint, Entity>(256, Allocator.Persistent);
            m_TempEntityList = new NativeList<Entity>(64, Allocator.Persistent);
            m_TempPoolTypeList = new NativeList<PoolTypeComponent>(64, Allocator.Persistent);
            m_TypeHashCounts = new NativeHashMap<uint, int>(32, Allocator.Persistent);
            m_EntityExistenceCache = new NativeBitArray(1024, Allocator.Persistent);
        }
        
#if UNITY_EDITOR
        private void ValidatePoolPrefabs(ref SystemState state)
        {
            // Validate that all pool prefabs have required components
            foreach (var (poolBuffer, entity) in SystemAPI.Query<DynamicBuffer<PoolEntry>>().WithEntityAccess())
            {
                foreach (var poolEntry in poolBuffer)
                {
                    var prefab = poolEntry.Prefab;
                    if (prefab == Entity.Null)
                    {
                        Debug.LogError($"Pool entry has null prefab on entity {entity}");
                        continue;
                    }
                    
                    // Check for required components
                    if (!state.EntityManager.HasComponent<Pooled>(prefab))
                        Debug.LogWarning($"Pool prefab {prefab} missing Pooled component");
                    if (!state.EntityManager.HasComponent<InUse>(prefab))
                        Debug.LogWarning($"Pool prefab {prefab} missing InUse component");
                }
            }
        }
#endif

        public void OnDestroy(ref SystemState state)
        {
            // Dispose all native collections
            if (m_TypeHashToEntityMap.IsCreated) m_TypeHashToEntityMap.Dispose();
            if (m_TempEntityList.IsCreated) m_TempEntityList.Dispose();
            if (m_TempPoolTypeList.IsCreated) m_TempPoolTypeList.Dispose();
            if (m_TypeHashCounts.IsCreated) m_TypeHashCounts.Dispose();
            if (m_EntityExistenceCache.IsCreated) m_EntityExistenceCache.Dispose();
        }

        // Cannot be Burst compiled due to ProcessReturnRequests calling managed EntitiesGraphicsSystem
        public void OnUpdate(ref SystemState state)
        {
            // Update all component handles and lookups once per frame
            UpdateHandlesAndLookups(ref state);
            
            // Clear and rebuild the type hash to entity map for fast lookups
            RebuildTypeHashMap(ref state);
            
            var ecbSystem = SystemAPI.GetSingleton<EndInitializationEntityCommandBufferSystem.Singleton>();
            var ecb = ecbSystem.CreateCommandBuffer(state.WorldUnmanaged);

            // 1) Initialize pools once at startup (Burst optimized)
            InitializePools(ref state, ecb);

            // 2) Process spawn requests (Burst optimized)
            ProcessSpawnRequests(ref state, ecb);

            // 3) Process return requests (hybrid: Burst + managed)
            ProcessReturnRequests(ref state, ecb);
            
#if UNITY_EDITOR
            // Validate internal consistency in editor
            state.EntityManager.Debug.CheckInternalConsistency();
#endif
        }
        
        private void UpdateHandlesAndLookups(ref SystemState state)
        {
            // Update component type handles
            m_PoolTypeHandle.Update(ref state);
            m_PooledHandle.Update(ref state);
            m_InUseHandle.Update(ref state);
            m_DisabledHandle.Update(ref state);
            m_TransformHandle.Update(ref state);
            m_PhysicsVelocityHandle.Update(ref state);
            
            // Update buffer type handles
            m_PoolEntryHandle.Update(ref state);
            m_SpawnRequestHandle.Update(ref state);
            m_ReturnRequestHandle.Update(ref state);
            m_RagdollColliderHandle.Update(ref state);
            m_ChildHandle.Update(ref state);
            m_LinkedEntityHandle.Update(ref state);
            
            // Update component lookups
            m_PoolTypeLookup.Update(ref state);
            m_PooledLookup.Update(ref state);
            m_InUseLookup.Update(ref state);
            m_DisabledLookup.Update(ref state);
            m_PhysicsVelocityLookup.Update(ref state);
            m_RagdollCharacterLookup.Update(ref state);
            m_EnemyTagLookup.Update(ref state);
            m_SpawnerLinkLookup.Update(ref state);
            
            // Update buffer lookups
            m_PoolEntryLookup.Update(ref state);
            m_RagdollColliderLookup.Update(ref state);
            m_ChildLookup.Update(ref state);
            m_LinkedEntityLookup.Update(ref state);
        }
        
        [BurstCompile]
        private void RebuildTypeHashMap(ref SystemState state)
        {
            // Clear the map and rebuild it for O(1) entity lookup by type hash
            m_TypeHashToEntityMap.Clear();
            
            // Use cached query and handles for maximum performance
            var chunks = m_PooledEntityQuery.ToArchetypeChunkArray(Allocator.Temp);
            
            foreach (var chunk in chunks)
            {
                var entities = chunk.GetNativeArray(state.GetEntityTypeHandle());
                var poolTypes = chunk.GetNativeArray(ref m_PoolTypeHandle);
                
                for (int i = 0; i < entities.Length; i++)
                {
                    m_TypeHashToEntityMap.Add(poolTypes[i].TypeHash, entities[i]);
                }
            }
            
            chunks.Dispose();
        }

        [BurstCompile]
        private void InitializePools(ref SystemState state, EntityCommandBuffer ecb)
        {
            // Use cached query - only runs once due to WithNone<PoolInitializedTag>
            if (m_PoolManagerQuery.IsEmpty) return;
            
            using var entities = m_PoolManagerQuery.ToEntityArray(Allocator.Temp);
            
            for (int i = 0; i < entities.Length; i++)
            {
                var entity = entities[i];
                var poolBuffer = SystemAPI.GetBuffer<PoolEntry>(entity);
                
                // Pre-count all existing pooled entities by type hash for efficiency
                var typeHashCounts = new NativeHashMap<uint, int>(poolBuffer.Length, Allocator.Temp);
                
                using var pooledEntities = m_PoolCountQuery.ToEntityArray(Allocator.Temp);
                using var poolTypes = m_PoolCountQuery.ToComponentDataArray<PoolTypeComponent>(Allocator.Temp);
                
                for (int j = 0; j < poolTypes.Length; j++)
                {
                    var typeHash = poolTypes[j].TypeHash;
                    if (typeHashCounts.TryGetValue(typeHash, out int count))
                        typeHashCounts[typeHash] = count + 1;
                    else
                        typeHashCounts[typeHash] = 1;
                }
                
                foreach (var entry in poolBuffer)
                {
                    // Get existing count from pre-computed hash map
                    int pooledCount = typeHashCounts.TryGetValue(entry.TypeHash, out int count) ? count : 0;
                    int toCreate = math.max(0, entry.PoolSize - pooledCount);
                    
                    for (int k = 0; k < toCreate; k++)
                    {
                        var pooledEntity = ecb.Instantiate(entry.Prefab);
                        ecb.AddComponent<Pooled>(pooledEntity);
                        ecb.AddComponent<InUse>(pooledEntity);
                        ecb.AddComponent<Disabled>(pooledEntity);
                        ecb.AddComponent(pooledEntity, new PoolTypeComponent { TypeHash = entry.TypeHash });
                        ecb.SetComponentEnabled<Pooled>(pooledEntity, true);
                        ecb.SetComponentEnabled<InUse>(pooledEntity, false);
                        
                        // Disable all linked entities (children) for newly instantiated pooled entities
                        // This will add Disabled to ALL entities in the LinkedEntityGroup, including root
                        ecb.AddComponentForLinkedEntityGroup<Disabled>(pooledEntity, m_AllArchetypesMask, new Disabled());
                        
                        //ecb.AddComponentForLinkedEntityGroup<Disabled>(pooledEntity, m_AllArchetypesMask,new Disabled());
                    }
                }
                
                typeHashCounts.Dispose();
                ecb.AddComponent<PoolInitializedTag>(entity);
            }
        }

        [BurstCompile]
        private void ProcessSpawnRequests(ref SystemState state, EntityCommandBuffer ecb)
        {
            // Use cached query
            if (m_SpawnRequestQuery.IsEmpty) return;
            
            using var entities = m_SpawnRequestQuery.ToEntityArray(Allocator.Temp);
            
            for (int i = 0; i < entities.Length; i++)
            {
                var managerEntity = entities[i];
                var spawnRequests = SystemAPI.GetBuffer<PoolSpawnRequest>(managerEntity);
                
                if (spawnRequests.Length == 0) continue;

                for (int j = 0; j < spawnRequests.Length; j++)
                {
                    var request = spawnRequests[j];

                    Entity pooledEntity = GetPooledEntity(ref state, request.TypeHash);

                    // If no pooled entity available, create new one
                    if (pooledEntity.Equals(Entity.Null))
                    {
                        pooledEntity = CreateNewPooledEntity(ref state, ecb, managerEntity, request.TypeHash);
                    }

                    if (!pooledEntity.Equals(Entity.Null))
                    {
                        ActivatePooledEntity(ref state, ecb, pooledEntity, request);
                    }
                }

                // Clear buffer via ECB for deterministic playback
                ecb.SetBuffer<PoolSpawnRequest>(managerEntity).Clear();
            }
        }

        // Cannot be Burst compiled due to ReturnToPool calling managed EntitiesGraphicsSystem
        private void ProcessReturnRequests(ref SystemState state, EntityCommandBuffer ecb)
        {
            // Use cached query
            if (m_ReturnRequestQuery.IsEmpty) return;
            
            using var entities = m_ReturnRequestQuery.ToEntityArray(Allocator.Temp);
            
            for (int i = 0; i < entities.Length; i++)
            {
                var managerEntity = entities[i];
                var returnRequests = SystemAPI.GetBuffer<PoolReturnRequest>(managerEntity);
                
                if (returnRequests.Length == 0) continue;

                for (int j = 0; j < returnRequests.Length; j++)
                {
                    var request = returnRequests[j];
                    ReturnToPool(ref state, ecb, request.EntityToReturn);
                }

                // Clear buffer via ECB for deterministic playback
                ecb.SetBuffer<PoolReturnRequest>(managerEntity).Clear();
            }
        }

        [BurstCompile]
        private Entity GetPooledEntity(ref SystemState state, uint typeHash)
        {
            // Use hash map for O(1) lookup instead of linear search
            if (m_TypeHashToEntityMap.TryGetFirstValue(typeHash, out Entity entity, out var iterator))
            {
                // Return the first available entity of this type
                return entity;
            }

            return Entity.Null;
        }
        
        [BurstCompile]
        private Entity GetPooledEntityWithFallback(ref SystemState state, uint typeHash)
        {
            // Try hash map first (O(1) lookup)
            if (m_TypeHashToEntityMap.TryGetFirstValue(typeHash, out Entity entity, out var iterator))
            {
                return entity;
            }
            
            // Fallback to direct query if hash map is empty (shouldn't happen in normal operation)
            var chunks = m_PooledEntityQuery.ToArchetypeChunkArray(Allocator.Temp);
            
            foreach (var chunk in chunks)
            {
                var entities = chunk.GetNativeArray(state.GetEntityTypeHandle());
                var poolTypes = chunk.GetNativeArray(ref m_PoolTypeHandle);
                
                for (int i = 0; i < entities.Length; i++)
                {
                    if (poolTypes[i].TypeHash == typeHash)
                    {
                        chunks.Dispose();
                        return entities[i];
                    }
                }
            }
            
            chunks.Dispose();
            return Entity.Null;
        }

        [BurstCompile]
        private Entity CreateNewPooledEntity(ref SystemState state, EntityCommandBuffer ecb, Entity managerEntity,
            uint typeHash)
        {
            var poolEntries = SystemAPI.GetBuffer<PoolEntry>(managerEntity);
            foreach (var entry in poolEntries)
            {
                if (entry.TypeHash == typeHash)
                {
                    var newEntity = ecb.Instantiate(entry.Prefab);
                    ecb.AddComponent<Pooled>(newEntity);
                    ecb.AddComponent<InUse>(newEntity);
                    ecb.AddComponent<Disabled>(newEntity);
                    ecb.AddComponent(newEntity, new PoolTypeComponent { TypeHash = typeHash });
                    ecb.SetComponentEnabled<Pooled>(newEntity, true);
                    ecb.SetComponentEnabled<InUse>(newEntity, false);
                    return newEntity;
                }
            }

            return Entity.Null;
        }

        // Cannot be Burst compiled due to complex entity existence checks and managed object access
        private void ActivatePooledEntity(ref SystemState state, EntityCommandBuffer ecb, Entity entity,
            PoolSpawnRequest request)
        {
            // For deferred entities (newly created), we can't use SystemAPI.HasComponent because the
            // entity hasn’t been played back yet. Determine whether the entity already exists:
            bool entityExists = state.EntityManager.Exists(entity);

            // Disable Pooled component (assume it exists for pooled entities)
            ecb.SetComponentEnabled<Pooled>(entity, false);

            // Enable InUse component (assume it exists for pooled entities)
            ecb.SetComponentEnabled<InUse>(entity, true);

            // Remove Disabled component (assume it exists for pooled entities)
            ecb.RemoveComponent<Disabled>(entity);

            // --- CHILD/LINKED/RAGDOLL ENABLE LOGIC ---
            if (entityExists)
            {
                
                if (SystemAPI.HasComponent<RagdollCharacterTag>(entity))
                {
                    // Re-enable ragdoll collider entities
                    if (SystemAPI.HasBuffer<RagdollColliderReference>(entity))
                    {
                        var colliderRefs = SystemAPI.GetBuffer<RagdollColliderReference>(entity);
                        foreach (var cr in colliderRefs)
                        {
                            if (SystemAPI.HasComponent<Disabled>(cr.ColliderEntity))
                                ecb.RemoveComponent<Disabled>(cr.ColliderEntity);
                        }
                    }
                }

                // Re-enable all child entities
                if (SystemAPI.HasBuffer<Child>(entity))
                {
                    var children = SystemAPI.GetBuffer<Child>(entity);
                    foreach (var child in children)
                    {
                        if (SystemAPI.HasComponent<Disabled>(child.Value))
                            ecb.RemoveComponent<Disabled>(child.Value);
                    }
                }

                // Re-enable all linked entities if prefab uses LinkedEntityGroup
                if (SystemAPI.HasBuffer<LinkedEntityGroup>(entity))
                {
                    var linkedEntities = SystemAPI.GetBuffer<LinkedEntityGroup>(entity);
                    foreach (var linked in linkedEntities)
                    {
                        if (linked.Value == entity) continue;
                        if (SystemAPI.HasComponent<Disabled>(linked.Value))
                            ecb.RemoveComponent<Disabled>(linked.Value);
                    }
                }
            }

            // First restore original state (materials, physics, etc.) but NOT position
            if (entityExists)
            {
                RestoreOriginalState(ref state, ecb, entity, skipPositionRestore: true);
            }
            
            // THEN set the new transform from the spawn request (this must come AFTER RestoreOriginalState)
            ecb.SetComponent(entity, new LocalTransform
            {
                Position = request.Position,
                Rotation = request.Rotation,
                Scale = request.Scale == 0f ? 1f : request.Scale
            });

            if (entityExists && SystemAPI.HasComponent<EnemyTag>(entity))
            {
                // Reset navigation components for proper pathfinding
                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentBody>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentBody>(entity, true);
                }

                //Disable agent collider first - will be re-enabled in next frame by NavigationRefreshSystem
                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentCollider>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentCollider>(entity, false);
                    // Add a tag to mark this entity needs navigation refresh
                    ecb.AddComponent<NavigationRefreshNeeded>(entity);
                }
            }

            // Add SpawnerLink component to track which spawner created this entity
            if (request.RequestingEntity != Entity.Null)
            {
                ecb.AddComponent(entity, new SpawnerLink
                {
                    SpawnerEntity = request.RequestingEntity,
                    SpawnerName = "PooledSpawner"
                });
            }

            // COMMUNITY FIX: Add marker component to indicate this entity was just activated from pool
            // This helps other systems (like RagdollSwapSystem) know to wait longer for physics rebuild
            if (entityExists)
            {
                ecb.AddComponent<JustActivatedFromPool>(entity);
            }
        }

        // Cannot be Burst compiled due to RestoreStateForEntity calling managed EntitiesGraphicsSystem
        private void RestoreOriginalState(ref SystemState state, EntityCommandBuffer ecb, Entity entity, bool skipPositionRestore = false)
        {
            // Restore position state (only when returning to pool, not when activating)
            if (!skipPositionRestore && SystemAPI.HasComponent<InitPositionState>(entity))
            {
                var positionState = SystemAPI.GetComponent<InitPositionState>(entity);
                ecb.SetComponent(entity, new LocalTransform
                {
                    Position = positionState.OriginalPosition,
                    Rotation = positionState.OriginalRotation,
                    Scale = 1f
                });
            }

            if (SystemAPI.HasComponent<RagdollCharacterTag>(entity))
            {
                // Restore material state - check both root entity and renderer entities
                RestoreStateForEntity(ref state, ecb, entity);

                // When activating from pool (skipPositionRestore = true), ensure cutoff is properly restored for visibility
                if (skipPositionRestore)
                {
                    RestoreCutoffStateForActivation(ref state, ecb, entity);
                }

                // Reset physics state
                ResetPhysicsState(ref state, ecb, entity);
            }
            
            // Remove temporary components added during ragdoll lifecycle
            RemoveTemporaryComponents(ref state, ecb, entity);
        }

        [BurstCompile]
        private void ResetPhysicsState(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            // STEP 1: Reset bone transforms to T-pose BEFORE disabling (Rukhanka 1.4+ requirement)
            ResetBoneTransformsToTPose(ref state, ecb, entity);

            // STEP 2: Force a one-frame "physics blackout" - disable entire hierarchy
            // This forces Unity Physics to rebuild the joint graph from prefab pose
            ForcePhysicsBlackout(ref state, ecb, entity);

            // STEP 3: Zero every physics buffer on all entities
            ResetEntityPhysicsState(ref state, ecb, entity);

            // Reset physics state for all ragdoll collider entities
            if (SystemAPI.HasBuffer<RagdollColliderReference>(entity))
            {
                var colliderRefs = SystemAPI.GetBuffer<RagdollColliderReference>(entity);
                foreach (var cr in colliderRefs)
                {
                    ResetEntityPhysicsState(ref state, ecb, cr.ColliderEntity);
                }
            }

            // Reset physics state for all child entities (in case some bodies are not in RagdollColliderReference)
            if (SystemAPI.HasBuffer<Child>(entity))
            {
                var children = SystemAPI.GetBuffer<Child>(entity);
                foreach (var child in children)
                {
                    ResetEntityPhysicsState(ref state, ecb, child.Value);
                }
            }

            // Reset physics state for all linked entities
            if (SystemAPI.HasBuffer<LinkedEntityGroup>(entity))
            {
                var linkedEntities = SystemAPI.GetBuffer<LinkedEntityGroup>(entity);
                foreach (var linked in linkedEntities)
                {
                    if (linked.Value != entity) // Skip the root entity as we already processed it
                    {
                        ResetEntityPhysicsState(ref state, ecb, linked.Value);
                    }
                }
            }
        }

        [BurstCompile]
        private void ResetEntityPhysicsState(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            var em = state.EntityManager;

            // Only proceed if entity exists
            if (!em.Exists(entity))
                return;

            // COMMUNITY FIX: Zero every physics buffer completely
            // Reset physics velocity to zero (both linear and angular)
            if (em.HasComponent<PhysicsVelocity>(entity))
            {
                ecb.SetComponent(entity, new PhysicsVelocity
                {
                    Linear = float3.zero,
                    Angular = float3.zero
                });
            }

            // COMMUNITY FIX: Clear interpolation history completely to prevent visual artifacts
            if (em.HasComponent<PhysicsGraphicalInterpolationBuffer>(entity))
            {
                ecb.SetComponent(entity, new PhysicsGraphicalInterpolationBuffer
                {
                    PreviousVelocity = PhysicsVelocity.Zero,
                    PreviousTransform = RigidTransform.identity // Reset to identity, not current transform
                });
            }

            // COMMUNITY FIX: Turn off interpolation during pooling
            // Remove smoothing components to prevent interpolation from stale data
            if (em.HasComponent<PhysicsGraphicalSmoothing>(entity))
            {
                ecb.RemoveComponent<PhysicsGraphicalSmoothing>(entity);
            }

            // Remove any physics mass overrides that might have been added during ragdoll lifetime
            if (em.HasComponent<PhysicsMassOverride>(entity))
            {
                ecb.RemoveComponent<PhysicsMassOverride>(entity);
            }

            // Remove any physics damping that might have been added during ragdoll lifetime
            if (em.HasComponent<PhysicsDamping>(entity))
            {
                ecb.RemoveComponent<PhysicsDamping>(entity);
            }
        }

        /// <summary>
        /// COMMUNITY FIX: Reset bone transforms to T-pose before disabling (Rukhanka 1.4+ requirement)
        /// This prevents joints from being solved relative to the previous death pose
        /// </summary>
        [BurstCompile]
        private void ResetBoneTransformsToTPose(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            var em = state.EntityManager;

            // Only proceed if entity exists
            if (!em.Exists(entity))
                return;

            // Reset root entity to original position/rotation from InitPositionState
            if (em.HasComponent<InitPositionState>(entity) && em.HasComponent<LocalTransform>(entity))
            {
                var initState = em.GetComponentData<InitPositionState>(entity);
                ecb.SetComponent(entity, new LocalTransform
                {
                    Position = initState.OriginalPosition,
                    Rotation = initState.OriginalRotation,
                    Scale = 1f
                });
            }

            // Reset all ragdoll collider entities to their reference poses
            if (em.HasBuffer<RagdollColliderReference>(entity))
            {
                var colliderRefs = em.GetBuffer<RagdollColliderReference>(entity);
                foreach (var cr in colliderRefs)
                {
                    // Only reset if collider entity exists and has LocalTransform
                    if (em.Exists(cr.ColliderEntity) && em.HasComponent<LocalTransform>(cr.ColliderEntity))
                    {
                        ecb.SetComponent(cr.ColliderEntity, LocalTransform.Identity);
                    }
                }
            }
        }

        /// <summary>
        /// COMMUNITY FIX: Force a one-frame "physics blackout" by disabling the entire entity hierarchy
        /// This forces Unity Physics to rebuild the joint graph from the prefab pose
        /// </summary>
        [BurstCompile]
        private void ForcePhysicsBlackout(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            var em = state.EntityManager;

            // Only proceed if entity exists
            if (!em.Exists(entity))
                return;

            // Add Disabled component to force physics world rebuild
            if (!em.HasComponent<Disabled>(entity))
            {
                ecb.AddComponent<Disabled>(entity);
            }

            // Disable all ragdoll collider entities
            if (em.HasBuffer<RagdollColliderReference>(entity))
            {
                var colliderRefs = em.GetBuffer<RagdollColliderReference>(entity);
                foreach (var cr in colliderRefs)
                {
                    if (em.Exists(cr.ColliderEntity) && !em.HasComponent<Disabled>(cr.ColliderEntity))
                    {
                        ecb.AddComponent<Disabled>(cr.ColliderEntity);
                    }
                }
            }

            // Disable all child entities
            if (em.HasBuffer<Child>(entity))
            {
                var children = em.GetBuffer<Child>(entity);
                foreach (var child in children)
                {
                    if (em.Exists(child.Value) && !em.HasComponent<Disabled>(child.Value))
                    {
                        ecb.AddComponent<Disabled>(child.Value);
                    }
                }
            }
        }

        // Cannot be Burst compiled due to EntitiesGraphicsSystem managed object access
        private void RestoreStateForEntity(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            // Check renderer entities in ragdoll collider buffer
            if (SystemAPI.HasBuffer<DissolveOverride>(entity))
            {
                var dissolveBuf = SystemAPI.GetBuffer<DissolveOverride>(entity);

                // 2. Grab the material we want to restore to
                //    (stored as a UnityEngine.Material reference on the root)
                var hasMatRef = SystemAPI.HasComponent<OriginalMaterialRef>(entity);

                // 3. For every renderer that belongs to this rag-doll
                foreach (var d in dissolveBuf)
                {
                    var renderer = d.RendererEntity;

                    // 1. Grab the material ID that Rukhanka already registered
                    if (hasMatRef && SystemAPI.HasComponent<MaterialMeshInfo>(renderer))
                    {
                        var egSystem = state.World.GetExistingSystemManaged<EntitiesGraphicsSystem>();
                        
                        var material = SystemAPI.GetComponent<OriginalMaterialRef>(entity);

                        var batchId = egSystem.RegisterMaterial(material.Asset);
                        
                        var mmi = SystemAPI.GetComponent<MaterialMeshInfo>(renderer);
                        mmi.MaterialID = batchId;
                        ecb.SetComponent(renderer, mmi); // keeps the same ID, just restores if it was overwritten
                    }

                    // 2. Restore cut-off
                    if (SystemAPI.HasComponent<InitCutoutState>(renderer))
                    {
                        var cut = SystemAPI.GetComponent<InitCutoutState>(renderer);
                        ecb.SetComponent(renderer, new URPCutoff { Value = cut.OriginalCutoffValue });
                        ecb.SetComponentEnabled<URPCutoff>(renderer, false);
                    }
                }
            }
        }

        // Restore cutoff state when activating from pool to ensure visibility
        private void RestoreCutoffStateForActivation(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            // Check renderer entities in ragdoll collider buffer
            if (SystemAPI.HasBuffer<DissolveOverride>(entity))
            {
                var dissolveBuf = SystemAPI.GetBuffer<DissolveOverride>(entity);

                // For every renderer that belongs to this ragdoll
                foreach (var d in dissolveBuf)
                {
                    var renderer = d.RendererEntity;

                    // Restore cut-off to original value and ENABLE the component for visibility
                    if (SystemAPI.HasComponent<InitCutoutState>(renderer))
                    {
                        var cut = SystemAPI.GetComponent<InitCutoutState>(renderer);
                        ecb.SetComponent(renderer, new URPCutoff { Value = cut.OriginalCutoffValue });
                        ecb.SetComponentEnabled<URPCutoff>(renderer, true); // ENABLE for visibility
                    }
                }
            }
        }

        [BurstCompile]
        private void RemoveTemporaryComponents(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            if (SystemAPI.HasComponent<RagdollCharacterTag>(entity))
            {
                // Remove components that were added during ragdoll lifecycle
                if (SystemAPI.HasComponent<RagdollIsFinished>(entity))
                {
                    ecb.RemoveComponent<RagdollIsFinished>(entity);
                }

                if (SystemAPI.HasComponent<DeathTimerComponent>(entity))
                {
                    ecb.RemoveComponent<DeathTimerComponent>(entity);
                }

                if (SystemAPI.HasComponent<DissolveTween>(entity))
                {
                    ecb.RemoveComponent<DissolveTween>(entity);
                }

                if (SystemAPI.HasComponent<SleepAfterSeconds>(entity))
                {
                    ecb.RemoveComponent<SleepAfterSeconds>(entity);
                }

                // Remove ragdoll physics impulse components that might be lingering
                if (SystemAPI.HasComponent<RagdollHitImpulse>(entity))
                {
                    ecb.RemoveComponent<RagdollHitImpulse>(entity);
                }

                if (SystemAPI.HasComponent<RagdollImpulseDelay>(entity))
                {
                    ecb.RemoveComponent<RagdollImpulseDelay>(entity);
                }

                // Remove any death-related tags
                if (SystemAPI.HasComponent<DeadTag>(entity))
                {
                    ecb.RemoveComponent<DeadTag>(entity);
                }
            }

            if (SystemAPI.HasComponent<EnemyTag>(entity))
            {
                ecb.RemoveComponent<UndetectableTag>(entity);
                ecb.RemoveComponent<DeadTag>(entity);
                
                if(SystemAPI.HasComponent<InFOVTag>(entity))
                    ecb.RemoveComponent<InFOVTag>(entity);
                
                var health = SystemAPI.GetComponent<HealthComponent>(entity);
                health.CurrentHealth = health.MaxHealth;
                ecb.SetComponent(entity, health);
            }
        }

        // Burst-optimized part of ReturnToPool - handles pooling state and navigation
        [BurstCompile]
        private void ReturnToPoolBurstOptimized(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            if (SystemAPI.HasComponent<EnemyTag>(entity))
            {
                // Reset navigation components before returning to pool
                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentBody>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentBody>(entity, false);
                }

                // Disable navigation components
                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentCollider>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentCollider>(entity, false);
                }

                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentSeparation>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentSeparation>(entity, false);
                }

                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentReciprocalAvoid>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentReciprocalAvoid>(entity, false);
                }
            }

            // Only set component enabled states if the components exist
            if (SystemAPI.HasComponent<Pooled>(entity))
            {
                ecb.SetComponentEnabled<Pooled>(entity, true);
            }
            else
            {
                ecb.AddComponent<Pooled>(entity);
                ecb.SetComponentEnabled<Pooled>(entity, true);
            }

            if (SystemAPI.HasComponent<InUse>(entity))
            {
                ecb.SetComponentEnabled<InUse>(entity, false);
            }
            else
            {
                ecb.AddComponent<InUse>(entity);
                ecb.SetComponentEnabled<InUse>(entity, false);
            }

            // Add Disabled component if it doesn't exist
            if (!SystemAPI.HasComponent<Disabled>(entity))
            {
                ecb.AddComponent<Disabled>(entity);
            }
            
            // Disable all child entities
            // if (SystemAPI.HasBuffer<Child>(entity))
            // {
            //     var children = SystemAPI.GetBuffer<Child>(entity);
            //     foreach (var child in children)
            //     {
            //         // Add Disabled if not already there
            //         if (!SystemAPI.HasComponent<Disabled>(child.Value))
            //             ecb.AddComponent<Disabled>(child.Value);
            //     }
            // }

            // Also disable all linked entities for prefabs that rely on LinkedEntityGroup instead of Child
            if (SystemAPI.HasBuffer<LinkedEntityGroup>(entity))
            {
                var linkedEntities = SystemAPI.GetBuffer<LinkedEntityGroup>(entity);
                foreach (var linked in linkedEntities)
                {
                    if (linked.Value == entity) continue; // root already handled
                    if (!SystemAPI.HasComponent<Disabled>(linked.Value))
                        ecb.AddComponent<Disabled>(linked.Value);
                }
            }

            if (SystemAPI.HasComponent<RagdollCharacterTag>(entity))
            {
                // Disable ragdoll collider entities (without re-parenting to avoid hierarchy issues)
                if (SystemAPI.HasBuffer<RagdollColliderReference>(entity))
                {
                    var colliderRefs = SystemAPI.GetBuffer<RagdollColliderReference>(entity);
                    foreach (var cr in colliderRefs)
                    {
                        // Add Disabled if not already there
                        if (!SystemAPI.HasComponent<Disabled>(cr.ColliderEntity))
                            ecb.AddComponent<Disabled>(cr.ColliderEntity);

                        // Comprehensive physics reset for each collider
                        ResetEntityPhysicsState(ref state, ecb, cr.ColliderEntity);
                    }
                }

            }

            // Remove SpawnerLink when returning to pool
            if (SystemAPI.HasComponent<SpawnerLink>(entity))
            {
                ecb.RemoveComponent<SpawnerLink>(entity);
            }
        }

        // Cannot be Burst compiled due to RestoreOriginalState calling managed EntitiesGraphicsSystem
        private void ReturnToPool(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            // Execute Burst-optimized part first
            ReturnToPoolBurstOptimized(ref state, ecb, entity);
            
            // Then execute managed part (material restoration)
            RestoreOriginalState(ref state, ecb, entity);
        }
        
        // Helper methods for efficient component operations
        [BurstCompile]
        private void SetComponentEnabledIfExists<T>(EntityCommandBuffer ecb, Entity entity, bool enabled) 
            where T : unmanaged, IComponentData, IEnableableComponent
        {
            if (m_DisabledLookup.HasComponent(entity))
            {
                ecb.SetComponentEnabled<T>(entity, enabled);
            }
        }
        
        [BurstCompile]
        private void AddComponentIfNotExists<T>(EntityCommandBuffer ecb, Entity entity, T component) 
            where T : unmanaged, IComponentData
        {
            if (!m_DisabledLookup.HasComponent(entity))
            {
                ecb.AddComponent(entity, component);
            }
        }
        
        [BurstCompile]
        private void ProcessEntityHierarchy(ref SystemState state, EntityCommandBuffer ecb, Entity rootEntity, bool enable)
        {
            // Process children
            if (m_ChildLookup.HasBuffer(rootEntity))
            {
                var children = m_ChildLookup[rootEntity];
                foreach (var child in children)
                {
                    if (enable)
                    {
                        if (m_DisabledLookup.HasComponent(child.Value))
                            ecb.RemoveComponent<Disabled>(child.Value);
                    }
                    else
                    {
                        if (!m_DisabledLookup.HasComponent(child.Value))
                            ecb.AddComponent<Disabled>(child.Value);
                    }
                }
            }
            
            // Process linked entities
            if (m_LinkedEntityLookup.HasBuffer(rootEntity))
            {
                var linkedEntities = m_LinkedEntityLookup[rootEntity];
                foreach (var linked in linkedEntities)
                {
                    if (linked.Value == rootEntity) continue;
                    
                    if (enable)
                    {
                        if (m_DisabledLookup.HasComponent(linked.Value))
                            ecb.RemoveComponent<Disabled>(linked.Value);
                    }
                    else
                    {
                        if (!m_DisabledLookup.HasComponent(linked.Value))
                            ecb.AddComponent<Disabled>(linked.Value);
                    }
                }
            }
            
            // Process ragdoll colliders
            if (m_RagdollCharacterLookup.HasComponent(rootEntity) && m_RagdollColliderLookup.HasBuffer(rootEntity))
            {
                var colliderRefs = m_RagdollColliderLookup[rootEntity];
                foreach (var cr in colliderRefs)
                {
                    if (enable)
                    {
                        if (m_DisabledLookup.HasComponent(cr.ColliderEntity))
                            ecb.RemoveComponent<Disabled>(cr.ColliderEntity);
                    }
                    else
                    {
                        if (!m_DisabledLookup.HasComponent(cr.ColliderEntity))
                            ecb.AddComponent<Disabled>(cr.ColliderEntity);

                        // Comprehensive physics reset for each collider
                        ResetEntityPhysicsState(ref state, ecb, cr.ColliderEntity);
                    }
                }
            }
        }
    }
}