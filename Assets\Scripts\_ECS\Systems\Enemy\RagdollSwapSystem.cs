using EditorTools;
using PlayerFAP.Authorings;
using PlayerFAP.Components;
using PlayerFAP.Systems.Health;
using ProjectDawn.Navigation.Sample.Crowd;
using RagdollSystem;
using Rukhanka;
using Rukhanka.Test;
using Rukhanka.Test.Pooling;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Physics;
using Unity.Physics.GraphicsIntegration;
using Unity.Transforms;

[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateAfter(typeof(DamageSystem))]
public partial struct RagdollSwapSystem : ISystem
{
    [ReadOnly] public ComponentLookup<RagdollHitImpulse> RagdollHitImpulseLookup;
    private Entity m_PoolManagerEntity;

    public void OnCreate(ref SystemState state)
    {
        state.RequireForUpdate<BeginSimulationEntityCommandBufferSystem.Singleton>();
        state.RequireForUpdate<EnemyPrefabDatabaseSingleton>();
        // Find pool manager entity
        var poolManagerQuery = state.EntityManager.CreateEntityQuery(ComponentType.ReadOnly<PoolManagerTag>());
        if (poolManagerQuery.CalculateEntityCount() > 0)
        {
            m_PoolManagerEntity = poolManagerQuery.GetSingletonEntity();
        }
        else
        {
            m_PoolManagerEntity = Entity.Null;
        }
    }

    public void OnUpdate(ref SystemState state)
    {
        var ecb = SystemAPI.GetSingleton<BeginSimulationEntityCommandBufferSystem.Singleton>()
            .CreateCommandBuffer(state.WorldUnmanaged);

        if (SystemAPI.HasSingleton<UsePoolingSystemTag>())
        {
            // Refresh pool manager entity in case it was created after this system
            if (m_PoolManagerEntity == Entity.Null || !state.EntityManager.Exists(m_PoolManagerEntity))
            {
                var poolManagerQuery = state.EntityManager.CreateEntityQuery(ComponentType.ReadOnly<PoolManagerTag>());
                if (poolManagerQuery.CalculateEntityCount() > 0)
                {
                    m_PoolManagerEntity = poolManagerQuery.GetSingletonEntity();
                }
            }

            foreach (var (transform, ragdollMapping, ragdollSwap, entity)
                     in SystemAPI.Query<RefRO<LocalTransform>, RefRO<EnemyRagdollMapping>,
                             RefRO<NeedsRagdollSwap>>()
                         .WithAll<NeedsRagdollSwap, AnimatedCharacterTag>()
                         .WithNone<WaitingForRagdollSpawn>()
                         .WithEntityAccess())
            {
                // Calculate hash for ragdoll type using PoolingUtility to match pool database
                uint typeHash = PoolingUtility.GetEnumHash(ragdollMapping.ValueRO.RagdollType);

#if UNITY_EDITOR
                UnityEngine.Debug.Log(
                    $"Requesting ragdoll for entity {entity}, type: {ragdollMapping.ValueRO.RagdollType}, hash: {typeHash}");
#endif

                // Request ragdoll from pool
                if (m_PoolManagerEntity != Entity.Null)
                {
                    ecb.AppendToBuffer(m_PoolManagerEntity, new PoolSpawnRequest
                    {
                        TypeHash = typeHash,
                        Position = transform.ValueRO.Position,
                        Rotation = transform.ValueRO.Rotation,
                        Scale = transform.ValueRO.Scale,
                        RequestingEntity = entity // Use the enemy entity as requester for tracking
                    });
                }
                else
                {
                    UnityEngine.Debug.LogError("No pool manager found for ragdoll spawning!");
                    continue;
                }
                
                // Add a component to track that this entity is waiting for ragdoll spawn
                ecb.AddComponent(entity, new WaitingForRagdollSpawn
                {
                    PlayerPosition = ragdollSwap.ValueRO.PlayerPosition,
                    HitPosition = ragdollSwap.ValueRO.HitPosition,
                    RagdollType = ragdollMapping.ValueRO.RagdollType
                });
                
                // Remove the swap component since we've processed it
                ecb.RemoveComponent<NeedsRagdollSwap>(entity);
            }

            // Process entities waiting for ragdoll spawn
            ProcessWaitingForRagdollSpawn(ref state, ecb);
        }
        else
        {
            var dbEntity = SystemAPI.GetSingletonEntity<EnemyPrefabDatabaseSingleton>();
            var buffer = state.EntityManager.GetBuffer<EnemyPrefabRef>(dbEntity);
            
             foreach (var (transform, enemyTypeComp,ragdollSwap, entity)
                     in SystemAPI.Query<RefRO<LocalTransform>, RefRO<EnemyTypeComponent> , RefRO<NeedsRagdollSwap>>()
                                                         .WithAll<NeedsRagdollSwap, AnimatedCharacterTag>()
                                                         .WithEntityAccess())
            {
                Entity ragdoll = Entity.Null;

                // If not found in pool, instantiate new from database
                int enemyType = enemyTypeComp.ValueRO.Value;
                
                // Instantiate ragdoll prefab for this enemy type
                Entity prefab = Entity.Null;
                foreach (var entry in buffer)
                {
                    if (entry.EnemyType == enemyType)
                    {
                        prefab = entry.RagdollPrefab;
                        break;
                    }
                }
                if (prefab == Entity.Null)
                {
                    UnityEngine.Debug.LogError($"No ragdoll prefab found for enemy type {enemyType}");
                    continue;
                }
                ragdoll = ecb.Instantiate(prefab);

                // copy transform so the ragdoll spawns exactly where the animated character was
                ecb.SetComponent(ragdoll, transform.ValueRO);
                ecb.AddComponent<RagdollCharacterTag>(ragdoll);
// Add DeadTag to mark entity as dead
                ecb.AddComponent<DeadTag>(ragdoll);
                // destroy the animated entity – swap done
                ecb.DestroyEntity(entity);

                float distance = math.distance(ragdollSwap.ValueRO.PlayerPosition,
                    ragdollSwap.ValueRO.HitPosition);

                float maxYFactor  = 10f;   // vertical multiplier at maxDistance
                float minYFactor  = 1f;    // vertical multiplier at zero distance
                float maxDistance = 10f;   // distance at which we cap the factor
                
                // Inverse-linear scale of yFactor
                float t       = math.saturate(distance / maxDistance);
                float yFactor = math.lerp(minYFactor, maxYFactor, t);

                float3 direction = math.normalize(ragdollSwap.ValueRO.HitPosition -
                                                  ragdollSwap.ValueRO.PlayerPosition) *
                                   new float3(1f, yFactor, 1f);

                float strength   = ComputeStrength(distance);
                float3 impulse   = direction * strength;

                // Add initial impulse and a 2-frame delay so physics is fully settled before applying
                ecb.AddComponent(ragdoll, new RagdollHitImpulse
                {
                    Impulse = impulse,
                    HitPosition = ragdollSwap.ValueRO.HitPosition,
                    PlayerPosition = ragdollSwap.ValueRO.PlayerPosition
                });
                
                ecb.AddComponent<RagdollImpulseDelay>(ragdoll, new RagdollImpulseDelay { Frames = 0 });
                ecb.SetComponentEnabled<InUse>(ragdoll,true);
                ecb.SetComponentEnabled<SleepAfterSeconds>(ragdoll,true);
            }
        }
    }

    private void ProcessWaitingForRagdollSpawn(ref SystemState state, EntityCommandBuffer ecb)
    {
        foreach (var (waitingComponent, entity) in SystemAPI.Query<RefRO<WaitingForRagdollSpawn>>()
                     .WithEntityAccess())
        {
#if UNITY_EDITOR
            UnityEngine.Debug.Log(
                $"Processing waiting entity {entity} for ragdoll type {waitingComponent.ValueRO.RagdollType}");
#endif
            // Check if there's a spawned ragdoll linked to this entity
            foreach (var (spawnerLink, ragdollEntity) in SystemAPI.Query<RefRO<SpawnerLink>>()
                         .WithAll<InUse>()
                         .WithNone<Disabled>()
                         .WithEntityAccess())
            {
#if UNITY_EDITOR
                UnityEngine.Debug.Log(
                    $"Found active ragdoll {ragdollEntity} with SpawnerEntity {spawnerLink.ValueRO.SpawnerEntity}, looking for {entity}");
#endif

                if (spawnerLink.ValueRO.SpawnerEntity == entity)
                {
                    // Found the ragdoll! Complete the swap
                    CompleteRagdollSwap(ref state, ecb, entity, ragdollEntity, waitingComponent.ValueRO);
                    break;
                }
            }
        }
    }

    private void CompleteRagdollSwap(ref SystemState state, EntityCommandBuffer ecb, Entity originalEntity,
        Entity ragdollEntity, WaitingForRagdollSpawn waitingData)
    {
#if UNITY_EDITOR
        UnityEngine.Debug.Log($"Completing ragdoll swap: {originalEntity} -> {ragdollEntity}");
#endif

        // Add essential ragdoll components like the working version
        ecb.AddComponent<DeadTag>(ragdollEntity);
        // Enable components that subsequent systems expect
        ecb.SetComponentEnabled<InUse>(ragdollEntity, true);

        // Add/enable SleepAfterSeconds component for SleepAndPoolSystem
        if (!SystemAPI.HasComponent<SleepAfterSeconds>(ragdollEntity))
        {
            ecb.AddComponent<SleepAfterSeconds>(ragdollEntity,
                new SleepAfterSeconds { TimeLeft = 2f }); // 5 second default
        }

        ecb.SetComponentEnabled<SleepAfterSeconds>(ragdollEntity, true);

        // Transfer any additional components from original entity to ragdoll
        TransferComponentsToRagdoll(ref state, ecb, originalEntity, ragdollEntity);
        
        float distance = math.distance(waitingData.PlayerPosition,
            waitingData.HitPosition);

        float maxYFactor = 10f; // vertical multiplier at maxDistance
        float minYFactor = 1f; // vertical multiplier at zero distance
        float maxDistance = 10f; // distance at which we cap the factor

        // Inverse-linear scale of yFactor
        float t = math.saturate(distance / maxDistance);
        float yFactor = math.lerp(minYFactor, maxYFactor, t);

        float3 direction = math.normalize(waitingData.HitPosition -
                                          waitingData.PlayerPosition) *
                           new float3(1f, yFactor, 1f);

        float strength = ComputeStrength(distance);
        float3 impulse = direction * strength;

        // Add initial impulse with appropriate delay
        ecb.AddComponent(ragdollEntity, new RagdollHitImpulse
        {
            Impulse = impulse,
            HitPosition = waitingData.HitPosition,
            PlayerPosition = waitingData.PlayerPosition
        });

        int framesToWait = SystemAPI.HasComponent<Disabled>(ragdollEntity) ? 3 : 1;

        ecb.AddComponent<RagdollImpulseDelay>(ragdollEntity, new RagdollImpulseDelay
        {
            Frames = 0
        });

        // Remove the waiting component
        ecb.RemoveComponent<WaitingForRagdollSpawn>(originalEntity);

        // Destroy the original animated character
        //ecb.DestroyEntity(originalEntity);
        PoolingUtility.RequestReturn(state.EntityManager, m_PoolManagerEntity, originalEntity);
    }

    static float ComputeStrength(float distance,
        float maxDistance = 10f,
        float maxStrength = 250f,
        float minStrength = 10f)
    {
        float t = math.saturate(distance / maxDistance);
        return math.lerp(maxStrength, minStrength, t);
    }

    private void TransferComponentsToRagdoll(ref SystemState state, EntityCommandBuffer ecb, Entity originalEntity,
        Entity ragdollEntity)
    {
        if (SystemAPI.HasBuffer<RagdollColliderReference>(ragdollEntity))
        {
            var ragdollColliders = SystemAPI.GetBuffer<RagdollColliderReference>(ragdollEntity);
            foreach (var ragdollColliderReference in ragdollColliders)
            {
                // 1. Stop movement
                if (SystemAPI.HasComponent<PhysicsVelocity>(ragdollColliderReference.ColliderEntity))
                {
                    var vel = SystemAPI.GetComponentRW<PhysicsVelocity>(ragdollColliderReference
                        .ColliderEntity);
                    vel.ValueRW.Linear = float3.zero;
                    vel.ValueRW.Angular = float3.zero;
                }
                
                if (SystemAPI.HasComponent<PhysicsGraphicalInterpolationBuffer>(ragdollColliderReference.ColliderEntity))
                {
                    ecb.SetComponent(ragdollColliderReference.ColliderEntity, new PhysicsGraphicalInterpolationBuffer
                    {
                        PreviousVelocity =  PhysicsVelocity.Zero,
                    });
                }
            }
        }

        // Set animator parameters after ragdoll spawn: Init=false, Death=true
        if (SystemAPI.HasComponent<AnimatorControllerParameterIndexTableComponent>(ragdollEntity))
        {
            var initParam = new FastAnimatorParameter("Init");
            var deathParam = new FastAnimatorParameter("Death");
            var animatorAspect = SystemAPI.GetAspect<AnimatorParametersAspect>(ragdollEntity);

            if (animatorAspect.HasParameter(initParam))
                animatorAspect.SetParameterValue(initParam, false);
            if (animatorAspect.HasParameter(deathParam))
                animatorAspect.SetParameterValue(deathParam, true);
        }
        
        // Transfer dissolve-related components for DissolveTweenToZeroSystem if they exist
        if (SystemAPI.HasComponent<DeathTimerComponent>(originalEntity))
        {
            var deathTimer = SystemAPI.GetComponent<DeathTimerComponent>(originalEntity);
            ecb.AddComponent(ragdollEntity, deathTimer);

#if UNITY_EDITOR
            UnityEngine.Debug.Log($"Transferred DeathTimerComponent to ragdoll {ragdollEntity}");
#endif
        }

        if (SystemAPI.HasComponent<DissolveTween>(originalEntity))
        {
            var dissolveTween = SystemAPI.GetComponent<DissolveTween>(originalEntity);
            ecb.AddComponent(ragdollEntity, dissolveTween);

#if UNITY_EDITOR
            UnityEngine.Debug.Log($"Transferred DissolveTween to ragdoll {ragdollEntity}");
#endif
        }

        if (SystemAPI.HasComponent<CutoffValue>(originalEntity))
        {
            var cutoffValue = SystemAPI.GetComponent<CutoffValue>(originalEntity);
            ecb.AddComponent(ragdollEntity, cutoffValue);

#if UNITY_EDITOR
            UnityEngine.Debug.Log($"Transferred CutoffValue to ragdoll {ragdollEntity}");
#endif
        }
    }
}

/// <summary>
/// Component to track entities waiting for ragdoll spawn from pool
/// </summary>
public struct WaitingForRagdollSpawn : IComponentData
{
    public float3 PlayerPosition;
    public float3 HitPosition;
    public EnemyRagdollType RagdollType;
}