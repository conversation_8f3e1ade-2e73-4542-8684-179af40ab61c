using PlayerFAP.Components;
using RagdollSystem;
using Rukhanka;
using Rukhanka.Test;
using Rukhanka.Test.Pooling;
using Unity.Burst;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Rendering;

public struct DissolveTween : IComponentData
{
    public float Duration; // seconds
    public float Elapsed; // accumulated time
}

[BurstCompile]
[UpdateAfter(typeof(SleepAndPoolSystem))]
public partial struct DissolveTweenToZeroSystem : ISystem
{
    private Entity m_PoolManagerEntity;
    private EntityQuery m_poolManagerQuery;

    public void OnCreate(ref SystemState state)
    {
        // Find pool manager entity
        m_poolManagerQuery = state.EntityManager.CreateEntityQuery(ComponentType.ReadOnly<PoolManagerTag>());
        if (m_poolManagerQuery.CalculateEntityCount() > 0)
        {
            m_PoolManagerEntity = m_poolManagerQuery.GetSingletonEntity();
        }
        else
        {
            m_PoolManagerEntity = Entity.Null;
        }

        state.RequireForUpdate<DissolveMaterialID>();
        state.RequireForUpdate<EndSimulationEntityCommandBufferSystem.Singleton>();
        state.RequireForUpdate<RagdollIsFinished>();
        state.RequireForUpdate<DeathTimerComponent>();
        state.RequireForUpdate<DissolveTween>();
        state.RequireForUpdate<CutoffValue>();
    }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        var dt = SystemAPI.Time.DeltaTime;
        var ecb = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>()
            .CreateCommandBuffer(state.WorldUnmanaged);


        foreach (var (timer, tween, meshRef, cutoff, entity) in
                 SystemAPI.Query<RefRO<DeathTimerComponent>,
                         RefRW<DissolveTween>,
                         DynamicBuffer<DissolveOverride>,
                         RefRO<CutoffValue>>()
                     .WithEntityAccess())
        {
            if (!timer.ValueRO.DissolveStarted)
                continue;

            foreach (var _mesh in meshRef)
            {
                // 1. Add the override component once
                var meshEntity = _mesh.RendererEntity;

                if (!SystemAPI.HasComponent<URPCutoff>(meshEntity))
                {
                    // Add and enable the material override for this renderer entity the first time we touch it
                    ecb.AddComponent(meshEntity, new URPCutoff { Value = 0f });
                }

                ecb.SetComponentEnabled<URPCutoff>(meshEntity, true);

                var materialId = SystemAPI.GetSingleton<DissolveMaterialID>().value;
                var mmi = SystemAPI.GetComponent<MaterialMeshInfo>(meshEntity);
                mmi.MaterialID = materialId;
                ecb.SetComponent(meshEntity, mmi);

                // 2. Update the cutoff value
                ref var t = ref tween.ValueRW;
                t.Elapsed += dt;
                var progress = math.saturate(t.Elapsed / t.Duration);
                progress = progress * progress * (3f - 2f * progress); // smooth-step
                var targetValue = math.lerp(cutoff.ValueRO.StartValue, cutoff.ValueRO.EndValue, progress);

                // Update URPCutoff component value
                ecb.SetComponent(meshEntity, new URPCutoff { Value = targetValue });

#if UNITY_EDITOR
                if (progress >= 1.0f)
                {
                    UnityEngine.Debug.Log($"[DissolveTweenToZeroSystem] Ragdoll {entity} dissolve complete, returning to pool. Final cutoff: {targetValue}");
                }
#endif


                // 3. Return entity to pool when dissolve is finished
                //    Return to pool instead of destroying to enable reuse
                if (progress >= 1.0f)
                {
                    if (m_PoolManagerEntity == Entity.Null || !state.EntityManager.Exists(m_PoolManagerEntity))
                    {
                        if (m_poolManagerQuery.CalculateEntityCount() > 0)
                        {
                            m_PoolManagerEntity = m_poolManagerQuery.GetSingletonEntity();
                        }
                    }
                    // Destroy the mesh/renderer entity first so it cannot render again
                    //ecb.DestroyEntity(meshEntity);

                    // Return the ragdoll entity to pool instead of destroying it
                    if (m_PoolManagerEntity != Entity.Null)
                    {
                        // Define animator parameters
                        var initParam = new FastAnimatorParameter("Init");
                        var deathParam = new FastAnimatorParameter("Death");

                        // Set animator parameters before returning to pool: Init=true, Death=false
                        if (SystemAPI.HasComponent<AnimatorControllerParameterIndexTableComponent>(entity))
                        {
                            var animatorAspect = SystemAPI.GetAspect<AnimatorParametersAspect>(entity);
                            if (animatorAspect.HasParameter(initParam))
                                animatorAspect.SetParameterValue(initParam, true);
                            if (animatorAspect.HasParameter(deathParam))
                                animatorAspect.SetParameterValue(deathParam, false);
                        }
                    }


                    if (SystemAPI.HasSingleton<UsePoolingSystemTag>())
                    {
                        // Set animator parameters after ragdoll spawn: Init=false, Death=true
                        if (SystemAPI.HasComponent<AnimatorControllerParameterIndexTableComponent>(entity))
                        {
                            var initParam = new FastAnimatorParameter("Init");
                            var deathParam = new FastAnimatorParameter("Death");
                            var animatorAspect = SystemAPI.GetAspect<AnimatorParametersAspect>(entity);

                            if (animatorAspect.HasParameter(initParam))
                                animatorAspect.SetParameterValue(initParam, true);
                            if (animatorAspect.HasParameter(deathParam))
                                animatorAspect.SetParameterValue(deathParam, false);
                        }
                        PoolingUtility.RequestReturn(state.EntityManager, m_PoolManagerEntity, entity);
                    }
                    else
                        ecb.DestroyEntity(entity);
                }
                else
                {
                    // Fallback to destroy if no pool manager found
                    //ecb.DestroyEntity(entity);
                }
            }
        }
    }
}