---
description: Repository Information Overview
alwaysApply: true
---

# Persian Arts Hybrid6.2 Unity Project Information

## Summary
This is a Unity game project using a hybrid approach combining traditional GameObject-based architecture with Unity's Entity Component System (ECS) and Data-Oriented Technology Stack (DOTS). The project appears to be a character-based game with combat mechanics, crowd simulation, and GPU-accelerated animation systems.

## Structure
- **Assets/**: Main Unity project assets including scripts, models, textures, and scenes
- **Assets/Scripts/**: Core game logic organized by functionality (Animation, Character, Movement, etc.)
- **Assets/Scripts/_ECS/**: ECS-specific implementation with Systems, Components, and Authoring scripts
- **Assets/Prefabs/**: Reusable game objects and entity templates
- **Assets/ScriptableObjects/**: Data-driven configuration objects
- **Packages/**: Unity packages including custom and third-party dependencies

## Language & Runtime
**Language**: C# (.NET)
**Unity Version**: 6000.1.11f1
**Build System**: Unity Build Pipeline
**Package Manager**: Unity Package Manager (UPM)

## Dependencies

### Core Unity Packages
- **com.unity.entities**: 1.3.14 (DOTS Entity Component System)
- **com.unity.entities.graphics**: 1.4.12 (DOTS rendering)
- **com.unity.physics**: 1.3.14 (DOTS physics)
- **com.unity.render-pipelines.universal**: 17.1.0 (URP rendering)
- **com.unity.inputsystem**: 1.14.0 (Input handling)
- **com.unity.cinemachine**: 2.10.4 (Camera system)

### Animation Systems
- **com.rukhanka.animation**: GPU-accelerated animation system
- **com.kybernetik.animancer**: Animation state machine system
- **GPUECSAnimationBakerEngine**: Custom GPU animation baking

### Navigation & AI
- **com.projectdawn.navigation**: ECS-compatible navigation
- **com.projectdawn.navigation.crowds**: Crowd simulation
- **ProjectDawn.ContinuumCrowds**: Advanced crowd movement

### Third-Party Tools
- **com.unity.memoryprofiler**: 1.1.6 (Memory optimization)
- **com.unity.project-auditor**: 1.0.1 (Project analysis)
- **MeshFusionPro**: Mesh combining for optimization
- **PerfectCulling**: Advanced culling system
- **Tayx.Graphy**: Performance monitoring

## Build & Installation
The project uses standard Unity build process:
```bash
# Development build
Unity.exe -projectPath "d:\UnityProject\PersianArts\Hybrid6.2" -buildTarget Win64 -executeMethod BuildScript.BuildDevelopment

# Release build
Unity.exe -projectPath "d:\UnityProject\PersianArts\Hybrid6.2" -buildTarget Win64 -executeMethod BuildScript.BuildRelease
```

## Testing
**Framework**: Unity Test Framework (com.unity.test-framework 1.5.1)
**Test Location**: Assets/Scripts/Tests/ and Assets/Scripts/_ECS/Test/
**Run Command**:
```bash
Unity.exe -projectPath "d:\UnityProject\PersianArts\Hybrid6.2" -runTests -testPlatform PlayMode
```

## Main Components

### ECS Architecture
The project uses a hybrid approach with both traditional MonoBehaviour components and DOTS ECS:
- **Systems**: Located in Assets/Scripts/_ECS/Systems/
- **Components**: Located in Assets/Scripts/_ECS/Components/
- **Authoring**: Located in Assets/Scripts/_ECS/Authoring/

### Animation Systems
Multiple animation systems are integrated:
- **Rukhanka**: GPU-accelerated animation system for crowds
- **Animancer**: State machine-based animation system
- **Unity Animation**: Traditional animation system

### Character & Movement
- **Player Systems**: Assets/Scripts/Player/ and Assets/Scripts/_ECS/Systems/Player/
- **Movement Systems**: Assets/Scripts/Movement/ and Assets/Scripts/_ECS/Systems/PlayerMovementSystem.cs
- **Navigation**: ProjectDawn navigation for AI movement

### Combat & Weapons
- **Weapon Systems**: Assets/Scripts/_ECS/Systems/Weapon/
- **Bullet Systems**: Assets/Scripts/_ECS/Systems/Bullet/
- **Detection Systems**: Assets/Scripts/_ECS/Systems/Detection/

### Optimization
- **GPU Animation**: Offloads animation to GPU for large crowds
- **Mesh Combining**: Multiple mesh optimization systems
- **Culling Systems**: Advanced culling for performance